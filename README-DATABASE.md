# 🏋️ Personal Trainer App - Database Schema

## 📋 Collections Overview

### 1. **users** (Auth Collection)
```
- email: string (system)
- password: string (system)
- name: string (required)
- age: number (optional, min: 13, max: 120)
- phone: string (optional, max: 20)
- avatar: file (optional, images only, 5MB max)
```

### 2. **subscriptions** (Base Collection)
```
- user: relation(users)
- plan_type: select('monthly', 'yearly')
- status: select('active', 'cancelled', 'expired')
- start_date: datetime
- end_date: datetime
- payment_method: select('pix', 'credit_card')
- amount: number
- asaas_subscription_id: string (optional)
```

### 3. **workout_months** (Base Collection)
```
- title: string (required) // "Janeiro 2025 - Foco em Cardio"
- description: text (optional)
- month: number (1-12)
- year: number (2025)
- cover_image: file (optional)
- is_active: bool (default: false) // mês atual
```

### 4. **workouts** (Base Collection)
```
- title: string (required) // "Treino de <PERSON> - Segunda"
- description: text (optional)
- video_file: file (required)
- thumbnail: file (optional)
- duration_minutes: number
- workout_month: relation(workout_months)
- order: number // ordem dentro do mês
- day_of_week: select('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday') (optional)
- likes_count: number (default: 0)
- views_count: number (default: 0)
```

### 5. **workout_likes** (Base Collection)
```
- user: relation(users)
- workout: relation(workouts)
```

### 6. **workout_views** (Base Collection)
```
- user: relation(users)
- workout: relation(workouts)
- completed: bool (default: false)
- progress_percentage: number (0-100, default: 0)
- viewed_at: datetime
```

## 🔗 Relacionamentos

- **users** → **subscriptions** (1:N)
- **workout_months** → **workouts** (1:N)
- **users** → **workout_likes** (1:N)
- **workouts** → **workout_likes** (1:N)
- **users** → **workout_views** (1:N)
- **workouts** → **workout_views** (1:N)

## 📊 Principais Queries

### Feed do mês atual
```javascript
// Buscar treinos do mês ativo
pb.collection('workouts').getList(1, 50, {
  filter: 'workout_month.is_active = true',
  sort: 'order',
  expand: 'workout_month'
})
```

### Histórico por mês
```javascript
// Treinos de um mês específico
pb.collection('workouts').getList(1, 50, {
  filter: 'workout_month.month = 1 && workout_month.year = 2025',
  sort: 'order',
  expand: 'workout_month'
})
```

### Progresso do usuário
```javascript
// Ver progresso atual
pb.collection('workout_views').getList(1, 50, {
  filter: `user = "${currentUser.id}"`,
  expand: 'workout'
})
```

### Treinos mais curtidos
```javascript
// Ranking de popularidade
pb.collection('workouts').getList(1, 10, {
  sort: '-likes_count'
})
```

## 🛡️ API Rules Sugeridas

### **users**
- List: `id = @request.auth.id`
- View: `id = @request.auth.id`
- Create: `` (qualquer um pode se registrar)
- Update: `id = @request.auth.id`
- Delete: `id = @request.auth.id`

### **subscriptions**
- List: `user = @request.auth.id`
- View: `user = @request.auth.id`
- Create: `user = @request.auth.id`
- Update: `user = @request.auth.id`
- Delete: `user = @request.auth.id`

### **workout_months**
- List: `` (todos podem ver)
- View: `` (todos podem ver)
- Create: `@request.auth.id != ""` (só logados)
- Update: `@request.auth.id != ""` (só logados)
- Delete: `@request.auth.id != ""` (só logados)

### **workouts**
- List: `` (todos podem ver)
- View: `` (todos podem ver)
- Create: `@request.auth.id != ""` (só logados - trainer)
- Update: `@request.auth.id != ""` (só logados - trainer)
- Delete: `@request.auth.id != ""` (só logados - trainer)

### **workout_likes**
- List: `user = @request.auth.id`
- View: `user = @request.auth.id`
- Create: `user = @request.auth.id`
- Update: `user = @request.auth.id`
- Delete: `user = @request.auth.id`

### **workout_views**
- List: `user = @request.auth.id`
- View: `user = @request.auth.id`
- Create: `user = @request.auth.id`
- Update: `user = @request.auth.id`
- Delete: `user = @request.auth.id`

## 💡 Implementação

1. **Criar collections manualmente** no PocketBase Admin
2. **Configurar API rules** para cada collection
3. **Testar relacionamentos** com dados de exemplo
4. **Implementar no frontend** SvelteKit + TypeScript

## 🎯 Funcionalidades Principais

- **Landing Page:** Conversão e checkout
- **Dashboard Aluna:** Feed mensal + histórico + perfil
- **Admin Trainer:** Upload de vídeos + organização
- **Sistema de Likes:** Feedback para trainer
- **Tracking de Progresso:** Gamificação e analytics
