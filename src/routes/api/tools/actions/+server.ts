import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { PocketBaseService } from '$lib/db/pocketbase';
import { POCKETBASE_URL } from '$env/static/private';

const pocketbaseService = new PocketBaseService(POCKETBASE_URL);

export const POST: RequestHandler = async ({ request }) => {
    try {
        const trackingData = await request.json();

        if (!trackingData.action) {
            return json({ error: 'Action is required' }, { status: 400 });
        }

        const savedRecord = await pocketbaseService.create('tracking', trackingData);
        return json({
            success: true,
            message: 'Tracking data stored successfully',
            id: savedRecord.id
        });

    } catch (error) {
        console.error('Failed to store tracking data:', error);
        return json({ error: 'Failed to store tracking data' }, { status: 500 });
    }
};