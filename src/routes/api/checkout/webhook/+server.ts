import { json } from '@sveltejs/kit';
import type { RequestHandler } from './confirm_payment/$types';
import { formatDate } from '$lib/utils';
import type { AsaasWebhookPayload } from '$lib/payment/types';

export const POST: RequestHandler = async ({ request, url }) => {
    try {
        const webhook: AsaasWebhookPayload = await request.json();
        console.log('📦 Webhook recebido:', {
            event: webhook.event,
            paymentId: webhook.payment.id,
            value: webhook.payment.value,
            status: webhook.payment.status,
            externalReference: webhook.payment.externalReference
        });

        switch (webhook.event) {
            case 'PAYMENT_RECEIVED':
                await handlePaymentReceived(webhook.payment, url);
                break;

            case 'PAYMENT_OVERDUE':
                await handlePaymentOverdue(webhook.payment, url);
                break;

            case 'PAYMENT_DELETED':
                await handlePaymentCanceled(webhook.payment, url);
                break;

            default:
                console.log('⚠️ Evento não tratado:', webhook.event);
        }

        return json({ received: true }, { status: 200 });

    } catch (error) {
        console.error('❌ Erro no webhook:', error);
        return json({ error: 'Webhook processing failed' }, { status: 500 });
    }
};

async function handlePaymentReceived(payment: AsaasWebhookPayload['payment'], url: URL) {
    console.log('✅ Pagamento aprovado:', payment.id);
    // Enviar notificação Discord
    await sendDiscordNotification(url, [{
        title: '💰 Pagamento Aprovado!',
        message: `**Valor:** R$ ${payment.value.toFixed(2).replace('.', ',')}\n**Método:** ${getBillingTypeLabel(payment.billingType)}\n**ID:** ${payment.id}\n**Referência:** ${payment.externalReference || 'N/A'}\n**Data:** ${formatDate(payment.paymentDate || payment.dateCreated)}`,
        color: '0x00ff00'
    }]);

    // TODO: Implementar suas ações específicas

    // Exemplo 1: Ativar plano premium
    if (payment.externalReference?.includes('plan_premium')) {
        const userId = extractUserIdFromReference(payment.externalReference);
        await activatePremiumPlan(userId);
    }

    // Exemplo 2: Processar compra de produto
    if (payment.externalReference?.includes('product_')) {
        const orderId = extractOrderIdFromReference(payment.externalReference);
        await processProductOrder(orderId);
    }

    // Exemplo 3: Enviar email de confirmação
    await sendPaymentConfirmationEmail(payment);

    // Exemplo 4: Salvar no banco de dados
    await savePaymentRecord(payment);
}

async function handlePaymentOverdue(payment: AsaasWebhookPayload['payment'], url: URL) {
    console.log('⏰ Pagamento vencido:', payment.id);

    // Enviar notificação Discord
    await sendDiscordNotification(url, [{
        title: '⏰ Pagamento Vencido!',
        message: `**Valor:** R$ ${payment.value.toFixed(2).replace('.', ',')}\n**Método:** ${getBillingTypeLabel(payment.billingType)}\n**ID:** ${payment.id}\n**Referência:** ${payment.externalReference || 'N/A'}\n**Vencimento:** ${formatDate(payment.dateCreated)}`,
        color: '0xff9900'
    }]);

    // TODO: Implementar ações para pagamento vencido
    // - Enviar email de lembrete
    // - Desativar serviços
    // - Marcar como inadimplente

    await sendOverdueNotification(payment);
}

async function handlePaymentCanceled(payment: AsaasWebhookPayload['payment'], url: URL) {
    console.log('❌ Pagamento cancelado:', payment.id);

    // Enviar notificação Discord
    await sendDiscordNotification(url, [{
        title: '❌ Pagamento Cancelado!',
        message: `**Valor:** R$ ${payment.value.toFixed(2).replace('.', ',')}\n**Método:** ${getBillingTypeLabel(payment.billingType)}\n**ID:** ${payment.id}\n**Referência:** ${payment.externalReference || 'N/A'}\n**Data:** ${formatDate(payment.dateCreated)}`,
        color: '0xff0000'
    }]);

    // TODO: Implementar ações para pagamento cancelado
    // - Liberar estoque
    // - Cancelar serviços
    // - Enviar notificação

    await handleOrderCancellation(payment);
}

// ========================================================================
// Discord Notification Helper
// ========================================================================

async function sendDiscordNotification(url: URL, messages: { title: string; message: string; color: string }[]) {
    try {
        const response = await fetch(`${url.origin}/api/tools/events`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ messages })
        });

        if (!response.ok) {
            console.error('❌ Falha ao enviar notificação Discord:', response.statusText);
        } else {
            console.log('✅ Notificação Discord enviada com sucesso');
        }
    } catch (error) {
        console.error('❌ Erro ao enviar notificação Discord:', error);
    }
}

function getBillingTypeLabel(billingType: string): string {
    const labels: Record<string, string> = {
        'PIX': '🏦 PIX',
        'CREDIT_CARD': '💳 Cartão de Crédito',
        'DEBIT_CARD': '💳 Cartão de Débito',
        'BOLETO': '📄 Boleto'
    };
    return labels[billingType] || billingType;
}

// ========================================================================
// Helper Functions (DUMMY - implementar conforme sua necessidade)
// ========================================================================

function extractUserIdFromReference(externalReference: string): string {
    // Exemplo: "plan_premium_user_123" -> "123"
    const parts = externalReference.split('_');
    return parts[parts.length - 1];
}

function extractOrderIdFromReference(externalReference: string): string {
    // Exemplo: "product_order_456" -> "456"
    const parts = externalReference.split('_');
    return parts[parts.length - 1];
}

async function activatePremiumPlan(userId: string) {
    console.log('🚀 Ativando plano premium para usuário:', userId);
    // TODO: Integrar com seu banco de dados
    // Exemplo com PocketBase:
    // await pb.collection('users').update(userId, {
    //     plan: 'premium',
    //     plan_expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 dias
    // });
}

async function processProductOrder(orderId: string) {
    console.log('📦 Processando pedido:', orderId);
    // TODO: Atualizar status do pedido
    // - Marcar como pago
    // - Iniciar processo de entrega
    // - Reduzir estoque
}

async function sendPaymentConfirmationEmail(payment: AsaasWebhookPayload['payment']) {
    console.log('📧 Enviando email de confirmação para pagamento:', payment.id);
    // TODO: Integrar com serviço de email
    // - Enviar comprovante
    // - Enviar informações do produto/serviço
    // - Incluir instruções de acesso
}

async function savePaymentRecord(payment: AsaasWebhookPayload['payment']) {
    console.log('💾 Salvando registro do pagamento:', payment.id);
    // TODO: Salvar no seu banco de dados
    // await pb.collection('payments').create({
    //     asaas_payment_id: payment.id,
    //     value: payment.value,
    //     status: payment.status,
    //     external_reference: payment.externalReference,
    //     payment_date: payment.paymentDate,
    //     created_at: new Date().toISOString()
    // });
}

async function sendOverdueNotification(payment: AsaasWebhookPayload['payment']) {
    console.log('⚠️ Enviando notificação de vencimento:', payment.id);
    // TODO: Enviar lembrete por email/SMS
    // TODO: Suspender serviços se necessário
}

async function handleOrderCancellation(payment: AsaasWebhookPayload['payment']) {
    console.log('🔄 Processando cancelamento:', payment.id);
    // TODO: Reverter ações do pedido
    // TODO: Liberar recursos
    // TODO: Notificar cliente
}