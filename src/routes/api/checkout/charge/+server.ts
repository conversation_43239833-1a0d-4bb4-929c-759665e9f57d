import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { AsaasClient } from '$lib/payment/asaas';
import type { AsaasCustomer } from '$lib/payment/types';
import { dev } from '$app/environment';

// Valor fixo da cobrança (R$ 50,00)
const CHARGE_VALUE = 50.00;

export const POST: RequestHandler = async ({ request }) => {
    try {
        // Parse dos dados do customer
        const { customer }: { customer: AsaasCustomer } = await request.json();

        // Validações básicas
        if (!customer.name || !customer.cpfCnpj) {
            return json({
                error: 'Nome e CPF/CNPJ são obrigatórios'
            }, { status: 400 });
        }

        // Inicializar cliente Asaas
        const asaas = new AsaasClient({
            apiKey: dev ? 'sandbox_api_key' : 'production_api_key',
            environment: dev ? 'sandbox' : 'production'
        });

        // Criar cobrança PIX com QR Code
        const result = await asaas.createPixCharge({
            customer,
            value: CHARGE_VALUE,
            description: 'Cobrança geral - Base Project',
            externalReference: `charge_${Date.now()}_${customer.cpfCnpj}`,
            daysToExpire: 1
        });

        // Retornar dados para o frontend
        return json({
            success: true,
            charge: {
                id: result.charge.id,
                value: result.charge.value,
                status: result.charge.status,
                dueDate: result.charge.dueDate
            },
            qrCode: {
                encodedImage: result.qrCode.encodedImage,
                payload: result.qrCode.payload,
                expirationDate: result.qrCode.expirationDate
            },
            customer: {
                id: result.customer.id,
                name: result.customer.name,
                cpfCnpj: result.customer.cpfCnpj
            }
        });

    } catch (error) {
        console.error('❌ Erro ao criar cobrança:', error);

        return json({
            error: 'Falha ao processar cobrança',
            details: error instanceof Error ? error.message : 'Erro desconhecido'
        }, { status: 500 });
    }
};