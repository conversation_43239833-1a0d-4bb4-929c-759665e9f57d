<script lang="ts">
	import Header from '$lib/components/landingpage/header.svelte';
	import AppWrapper from '$lib/components/ui/app-wrapper.svelte';
	import Footer from '$lib/components/landingpage/footer.svelte';

	let { children: child } = $props();
</script>

<AppWrapper
	mode="normal"
	header={{ show: true, position: 'fixed' }}
	footer={{ show: true, position: 'default' }}
>
	{#snippet headerSnippet()}
		<Header />
	{/snippet}

	{#snippet children()}
		{@render child?.()}
	{/snippet}

	{#snippet footerSnippet()}
		<Footer />
	{/snippet}
</AppWrapper>
