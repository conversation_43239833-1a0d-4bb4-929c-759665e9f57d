import PocketBase from 'pocketbase';

export class PocketBaseService {
    private pb: PocketBase;

    constructor(pocketbaseUrl: string) {
        this.pb = new PocketBase(pocketbaseUrl);
    }

    getClient() {
        return this.pb;
    }

    async loginWithGoogle() {
        return this.pb.collection('users').authWithOAuth2({
            provider: 'google'
        });
    }

    async logoutUser() {
        this.pb.authStore.clear();
    }

    isUserAuthenticated() {
        return this.pb.authStore.isValid;
    }

    getCurrentUser() {
        return this.pb.authStore.record;
    }

    getCurrentToken() {
        return this.pb.authStore.token;
    }

    // CRUD operations
    async create(collection: string, data: any) {
        return this.pb.collection(collection).create(data);
    }

    async getList(collection: string, page = 1, perPage = 50, filter?: string, sort?: string) {
        return this.pb.collection(collection).getList(page, perPage, {
            filter,
            sort
        });
    }

    async getOne(collection: string, id: string) {
        return this.pb.collection(collection).getOne(id);
    }

    async update(collection: string, id: string, data: any) {
        return this.pb.collection(collection).update(id, data);
    }

    async delete(collection: string, id: string) {
        return this.pb.collection(collection).delete(id);
    }
}