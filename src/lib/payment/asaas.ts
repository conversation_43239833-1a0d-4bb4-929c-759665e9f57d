import { dev } from '$app/environment';
import type { <PERSON><PERSON><PERSON><PERSON>onfig, As<PERSON><PERSON><PERSON>ustomer, AsaasCharge, AsaasChargeResponse, AsaasPixQRCode } from './types';

const DEFAULT_CONFIG: Partial<AsaasConfig> = {
    environment: dev ? 'sandbox' : 'production',
    baseUrl: dev ? 'https://sandbox.asaas.com/api/v3' : 'https://api.asaas.com/v3'
};

export class AsaasClient {
    private config: AsaasConfig;

    constructor(config: AsaasConfig) {
        this.config = {
            ...DEFAULT_CONFIG,
            ...config
        };
    }

    private async request<T>(
        endpoint: string,
        options: RequestInit = {}
    ): Promise<T> {
        const url = `${this.config.baseUrl}${endpoint}`;

        const response = await fetch(url, {
            headers: {
                'accept': 'application/json',
                'content-type': 'application/json',
                'access_token': this.config.apiKey,
                ...options.headers
            },
            ...options
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(`Asaas API Error: ${response.status} - ${errorData.message || response.statusText}`);
        }

        return response.json();
    }

    // ========================================================================
    // Customer Methods
    // ========================================================================

    /**
     * Create a new customer
     */
    async createCustomer(customer: AsaasCustomer): Promise<AsaasCustomer> {
        return this.request<AsaasCustomer>('/customers', {
            method: 'POST',
            body: JSON.stringify(customer)
        });
    }

    /**
     * Get customer by ID
     */
    async getCustomer(customerId: string): Promise<AsaasCustomer> {
        return this.request<AsaasCustomer>(`/customers/${customerId}`);
    }

    /**
     * Update customer
     */
    async updateCustomer(customerId: string, customer: Partial<AsaasCustomer>): Promise<AsaasCustomer> {
        return this.request<AsaasCustomer>(`/customers/${customerId}`, {
            method: 'POST',
            body: JSON.stringify(customer)
        });
    }

    /**
     * Find customer by CPF/CNPJ
     */
    async findCustomerByCpfCnpj(cpfCnpj: string): Promise<AsaasCustomer[]> {
        const response = await this.request<{ data: AsaasCustomer[] }>(`/customers?cpfCnpj=${cpfCnpj}`);
        return response.data;
    }

    // ========================================================================
    // Charge Methods
    // ========================================================================

    /**
     * Create a new charge
     */
    async createCharge(charge: AsaasCharge): Promise<AsaasChargeResponse> {
        return this.request<AsaasChargeResponse>('/payments', {
            method: 'POST',
            body: JSON.stringify(charge)
        });
    }

    /**
     * Get charge by ID
     */
    async getCharge(chargeId: string): Promise<AsaasChargeResponse> {
        return this.request<AsaasChargeResponse>(`/payments/${chargeId}`);
    }

    /**
     * Cancel charge
     */
    async cancelCharge(chargeId: string): Promise<AsaasChargeResponse> {
        return this.request<AsaasChargeResponse>(`/payments/${chargeId}`, {
            method: 'DELETE'
        });
    }

    /**
     * Get PIX QR Code for a charge
     */
    async getPixQRCode(chargeId: string): Promise<AsaasPixQRCode> {
        const response = await this.request<{ qrCode: AsaasPixQRCode }>(`/payments/${chargeId}/pixQrCode`);
        return response.qrCode;
    }

    // ========================================================================
    // Convenience Methods
    // ========================================================================

    /**
     * Create PIX charge with QR Code - Most common use case
     */
    async createPixCharge(params: {
        customer: AsaasCustomer;
        value: number;
        description?: string;
        externalReference?: string;
        daysToExpire?: number;
    }): Promise<{
        charge: AsaasChargeResponse;
        qrCode: AsaasPixQRCode;
        customer: AsaasCustomer;
    }> {
        // Create or find customer
        let customer = params.customer;
        if (!customer.id) {
            // Try to find existing customer by CPF
            const existingCustomers = await this.findCustomerByCpfCnpj(customer.cpfCnpj);
            if (existingCustomers.length > 0) {
                customer = existingCustomers[0];
            } else {
                customer = await this.createCustomer(customer);
            }
        }

        // Calculate due date
        const dueDate = new Date();
        dueDate.setDate(dueDate.getDate() + (params.daysToExpire || 1));

        // Create PIX charge
        const charge = await this.createCharge({
            customer: customer.id!,
            billingType: 'PIX',
            value: params.value,
            description: params.description,
            externalReference: params.externalReference,
            dueDate: dueDate.toISOString().split('T')[0] // YYYY-MM-DD
        });

        // Get QR Code
        const qrCode = await this.getPixQRCode(charge.id);

        return {
            charge,
            qrCode,
            customer
        };
    }

    /**
     * Check charge status - useful for polling
     */
    async isChargePaid(chargeId: string): Promise<boolean> {
        const charge = await this.getCharge(chargeId);
        return ['RECEIVED', 'CONFIRMED', 'RECEIVED_IN_CASH'].includes(charge.status);
    }
}