export interface AsaasConfig {
    apiKey: string;
    environment: 'sandbox' | 'production';
    baseUrl?: string;
}

export interface AsaasCustomer {
    id?: string;
    name: string;
    cpfCnpj: string;
    email?: string;
    phone?: string;
    mobilePhone?: string;
    address?: string;
    addressNumber?: string;
    complement?: string;
    province?: string;
    city?: string;
    state?: string;
    postalCode?: string;
}

export interface AsaasCharge {
    id?: string;
    customer: string; // Customer ID
    billingType: 'BOLETO' | 'CREDIT_CARD' | 'PIX' | 'DEBIT_CARD' | 'TRANSFER' | 'DEPOSIT';
    value: number;
    dueDate: string; // YYYY-MM-DD
    description?: string;
    externalReference?: string;
    installmentCount?: number;
    installmentValue?: number;
    discount?: {
        value: number;
        dueDateLimitDays: number;
    };
    interest?: {
        value: number;
    };
    fine?: {
        value: number;
    };
    postalService?: boolean;
}

export interface AsaasChargeResponse {
    id: string;
    status: 'PENDING' | 'RECEIVED' | 'CONFIRMED' | 'OVERDUE' | 'RECEIVED_IN_CASH' | 'REFUNDED' | 'CANCELLED';
    customer: string;
    value: number;
    netValue: number;
    originalValue?: number;
    interestValue?: number;
    description?: string;
    billingType: string;
    pixTransaction?: {
        qrCode: {
            payload: string;
            encodedImage: string;
        };
        expirationDate: string;
    };
    invoiceUrl: string;
    bankSlipUrl?: string;
    transactionReceiptUrl?: string;
    dueDate: string;
    originalDueDate: string;
    paymentDate?: string;
    clientPaymentDate?: string;
    installmentNumber?: number;
    invoiceNumber?: string;
    externalReference?: string;
    deleted: boolean;
    anticipated: boolean;
    anticipable: boolean;
    creditDate?: string;
    estimatedCreditDate?: string;
    transactionReceiptEmail?: string;
    nossoNumero?: string;
    lastInvoiceViewedDate?: string;
    lastBankSlipViewedDate?: string;
    discount?: any;
    fine?: any;
    interest?: any;
    split?: any[];
}

export interface AsaasPixQRCode {
    encodedImage: string; // Base64 QR Code image
    payload: string; // PIX copia e cola
    expirationDate: string;
}

export interface AsaasWebhookPayload {
    event: 'PAYMENT_RECEIVED' | 'PAYMENT_OVERDUE' | 'PAYMENT_DELETED' | 'PAYMENT_AWAITING_CHARGEBACK' | 'PAYMENT_CHARGEBACK_REQUESTED';
    payment: {
        id: string;
        customer: string;
        value: number;
        status: string;
        billingType: 'PIX' | 'CREDIT_CARD' | 'BOLETO' | 'DEBIT_CARD';
        externalReference?: string;
        dateCreated: string;
        paymentDate?: string;
        description?: string;
    };
}