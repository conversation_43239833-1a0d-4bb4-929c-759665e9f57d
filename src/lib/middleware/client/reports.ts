import type { HandleClientError } from '@sveltejs/kit';
import reportsService from '$lib/tools/reports';

export const clientErrorReporting: HandleClientError = async ({ error, event, status, message }) => {
    try {
        if (isReportableError(error, status)) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown client error occurred';
            const errorType = getErrorType(error);

            await reportsService.report(
                `🖥️ [CLIENT] ${errorType}`,
                errorMessage,
                getSeverityByErrorType(errorType),
                {
                    functionName: 'clientErrorHandler',
                    error: error instanceof Error ? error : undefined,
                    metadata: {
                        errorType: errorType.toLowerCase().replace(' ', '_'),
                        route: event.route?.id || 'unknown',
                        pageUrl: typeof window !== 'undefined' ? window.location.href : 'unknown',
                        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
                        viewport: typeof window !== 'undefined' ?
                            `${window.innerWidth}x${window.innerHeight}` : 'unknown',
                        online: typeof navigator !== 'undefined' ? navigator.onLine : true,
                        httpStatus: status
                    }
                }
            );
        }
    } catch (reportError) {
        console.error('Failed to report client error:', reportError);
    }

    return {
        message: 'Something went wrong on the client',
        code: (error instanceof Error && 'code' in error ? error.code : 'CLIENT_ERROR') as string
    };
};

function isReportableError(error: unknown, status?: number): boolean {
    if (status && status >= 500) {
        return false;
    }

    if (error instanceof Error) {
        const jsErrors = [
            'ReferenceError',
            'TypeError',
            'SyntaxError',
            'RangeError',
            'EvalError',
            'URIError'
        ];

        return jsErrors.some(errorType =>
            error.constructor.name === errorType ||
            error.name === errorType
        );
    }

    return true;
}

function getErrorType(error: unknown): string {
    if (error instanceof Error) {
        switch (error.constructor.name) {
            case 'ReferenceError':
                return 'Reference Error';
            case 'TypeError':
                return 'Type Error';
            case 'SyntaxError':
                return 'Syntax Error';
            case 'RangeError':
                return 'Range Error';
            case 'EvalError':
                return 'Eval Error';
            case 'URIError':
                return 'URI Error';
            default:
                return 'Unhandled Error';
        }
    }

    return 'Unknown Error';
}

function getSeverityByErrorType(errorType: string): 'low' | 'medium' | 'high' | 'critical' {
    switch (errorType) {
        case 'Reference Error':
        case 'Type Error':
            return 'high';
        case 'Syntax Error':
            return 'critical';
        case 'Range Error':
        case 'Eval Error':
        case 'URI Error':
            return 'medium';
        default:
            return 'medium';
    }
}


