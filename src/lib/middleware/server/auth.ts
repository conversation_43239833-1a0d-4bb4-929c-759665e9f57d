import { redirect } from '@sveltejs/kit';
import type { <PERSON><PERSON> } from '@sveltejs/kit';
import { PocketBaseService } from '$lib/db/pocketbase';
import { dev } from '$app/environment';

export const authMiddleware: Handle = async ({ event, resolve }) => {
    const token = event.request.headers.get('authorization')?.replace('Bearer ', '') ||
        event.cookies.get('pb_auth') ||
        event.url.searchParams.get('token') || null;

    if (event.url.pathname.startsWith('/app')) {
        if (!token) {
            throw redirect(302, '/');
        }

        const user = await validateTokenAndGetUser(token);
        if (!user) {
            throw redirect(302, '/logout');
        }

        event.locals.user = user;
    }

    return resolve(event);
};

async function validateTokenAndGetUser(token: string): Promise<any> {
    try {
        const pbUrl = dev ? 'http://127.0.0.1:8090' : import.meta.env.VITE_POCKETBASE_URL;
        const pb = new PocketBaseService(pbUrl);

        pb.getClient().authStore.save(token);
        await pb.getClient().collection('users').authRefresh();

        if (pb.isUserAuthenticated()) {
            return pb.getCurrentUser();
        }

        return null;
    } catch (error) {
        console.error('❌ Token inválido:', error);
        return null;
    }
}