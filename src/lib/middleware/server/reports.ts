import type { Handle, HandleServerError } from '@sveltejs/kit';
import { DiscordNotifier } from '$lib/tools/discord';
import { DISCORD_WEBHOOK_URL } from '$env/static/private';
import { dev } from '$app/environment';

export const errorReporting: Handle = async ({ event, resolve }) => {
    try {
        const response = await resolve(event);
        if (response.status >= 500) {
            await reportServerError({
                title: '🚨 Server Error Response',
                description: `HTTP ${response.status} error occurred`,
                url: event.url.pathname,
                method: event.request.method,
                userAgent: event.request.headers.get('user-agent'),
                clientIP: event.getClientAddress(),
                timestamp: new Date().toISOString()
            });
        }

        return response;
    } catch (error: any) {
        let stackTrace: string;
        if (typeof error === 'string') {
            stackTrace = error;
        } else if (error && typeof error === 'object') {
            try {
                stackTrace = JSON.stringify(error, null, 2);
            } catch {
                stackTrace = String(error);
            }
        } else {
            stackTrace = String(error);
        }

        await reportServerError({
            title: '💥 Unhandled Server Error',
            description: error instanceof Error ? error.message : 'Unknown error occurred',
            url: event.url.pathname,
            method: event.request.method,
            userAgent: event.request.headers.get('user-agent'),
            clientIP: event.getClientAddress(),
            stackTrace,
            timestamp: new Date().toISOString()
        });

        throw error;
    }
};

async function reportServerError(errorData: {
    title: string;
    description: string;
    url: string;
    method: string;
    userAgent: string | null;
    clientIP: string;
    stackTrace?: string;
    timestamp: string;
}) {
    if (dev) {
        console.error('Server Error (dev mode):', errorData);
        return;
    }

    try {
        const notifier = new DiscordNotifier(DISCORD_WEBHOOK_URL, 'Server Error Reporter');
        const messages = [
            {
                title: errorData.title,
                message: errorData.description
            },
            {
                title: '🌐 Request Details',
                message: `Method: ${errorData.method}\nURL: ${errorData.url}\nIP: ${errorData.clientIP}`
            },
            {
                title: '🕒 Timestamp',
                message: errorData.timestamp
            }
        ];

        if (errorData.userAgent) {
            messages.push({
                title: '🖥️ User Agent',
                message: errorData.userAgent.substring(0, 500)
            });
        }

        if (errorData.stackTrace) {
            messages.push({
                title: '📋 Stack Trace',
                message: errorData.stackTrace.substring(0, 1500)
            });
        }

        await notifier.sendNotification(messages);
    } catch (reportError) {
        console.error('Failed to report server error:', reportError);
        console.error('Original error data:', errorData);
    }
}

export const handleError: HandleServerError = async ({ error, event }) => {
    const errorId = crypto.randomUUID();

    await reportServerError({
        title: '⚠️ SvelteKit Unhandled Error',
        description: `Error ID: ${errorId}\n${error instanceof Error ? error.message : 'Unknown error'}`,
        url: event.url.pathname,
        method: event.request.method,
        userAgent: event.request.headers.get('user-agent'),
        clientIP: event.getClientAddress(),
        stackTrace: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
    });

    return {
        message: `An error occurred. Error ID: ${errorId}`,
        errorId
    };
};