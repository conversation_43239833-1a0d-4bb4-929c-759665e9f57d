<svg
	viewBox="0 0 100 100"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	class="text-primary h-8 w-8"
>
	<!-- Círculo externo pulsante -->
	<circle
		cx="50"
		cy="50"
		r="45"
		stroke="currentColor"
		stroke-width="2"
		fill="none"
		opacity="0.3"
		class="pulse-ring"
	/>

	<!-- <PERSON><PERSON><PERSON><PERSON> médio -->
	<circle
		cx="50"
		cy="50"
		r="30"
		stroke="currentColor"
		stroke-width="1.5"
		fill="none"
		opacity="0.5"
		class="pulse-ring-2"
	/>

	<!-- Cora<PERSON> central -->
	<path
		d="M50 75 C35 60, 20 45, 20 30 C20 20, 30 15, 40 20 C45 15, 50 15, 50 25 C50 15, 55 15, 60 20 C70 15, 80 20, 80 30 C80 45, 65 60, 50 75Z"
		fill="currentColor"
		class="heart-beat"
	/>

	<!-- Pontos de energia orbitando -->
	<circle r="3" fill="currentColor" opacity="0.7" class="orbit-dot-1">
		<animateTransform
			attributeName="transform"
			type="rotate"
			values="0 50 50;360 50 50"
			dur="4s"
			repeatCount="indefinite"
		/>
	</circle>

	<circle r="2" fill="currentColor" opacity="0.5" class="orbit-dot-2">
		<animateTransform
			attributeName="transform"
			type="rotate"
			values="180 50 50;540 50 50"
			dur="6s"
			repeatCount="indefinite"
		/>
	</circle>
</svg>

<style>
	/* Animação do coração pulsante */
	.heart-beat {
		animation: heartbeat 2s ease-in-out infinite;
		transform-origin: center;
	}

	@keyframes heartbeat {
		0%,
		100% {
			transform: scale(1);
		}
		25% {
			transform: scale(1.05);
		}
		50% {
			transform: scale(1);
		}
		75% {
			transform: scale(1.02);
		}
	}

	/* Animação dos anéis pulsantes */
	.pulse-ring {
		animation: pulse-ring 3s ease-in-out infinite;
	}

	.pulse-ring-2 {
		animation: pulse-ring 3s ease-in-out infinite 0.5s;
	}

	@keyframes pulse-ring {
		0%,
		100% {
			transform: scale(1);
			opacity: 0.3;
		}
		50% {
			transform: scale(1.1);
			opacity: 0.1;
		}
	}

	/* Posicionamento dos pontos orbitais */
	.orbit-dot-1 {
		transform-origin: 50px 50px;
		cx: 75;
		cy: 50;
	}

	.orbit-dot-2 {
		transform-origin: 50px 50px;
		cx: 25;
		cy: 50;
	}

	/* Respeita preferências de movimento reduzido */
	@media (prefers-reduced-motion: reduce) {
		.heart-beat,
		.pulse-ring,
		.pulse-ring-2 {
			animation: none;
		}

		.orbit-dot-1 animateTransform,
		.orbit-dot-2 animateTransform {
			animation-duration: 0s;
		}
	}
</style>
