<script>
	import { globalState } from '$lib/states/global.state.svelte';
	import Button from '../ui/button/button.svelte';
	import Icon from '../ui/icon.svelte';
	import { scale } from 'svelte/transition';

	let firstBubble = $state(false);
	let secondBubble = $state(false);

	$effect(() => {
		firstBubble = true;
		secondBubble = true;
	});
</script>

<section class="relative mx-auto max-w-[1200px] px-6 py-20">
	<!-- Badge/Announcement -->
	<div class="mb-8 flex justify-center">
		<div
			class="from-accent/10 to-primary/10 inline-flex items-center gap-2 rounded-full bg-gradient-to-r px-4 py-2 text-sm font-medium"
		>
			<span class="bg-primary flex h-2 w-2 animate-pulse rounded-full"></span>
			<span class="text-muted-foreground max-[400px]:hidden">Novo</span>
			<span class="text-foreground">Treinos di<PERSON> disponíveis</span>
			<Icon icon="lucide:arrow-right" class="text-muted-foreground h-4 w-4" />
		</div>
	</div>

	<!-- Main Hero Content -->
	<div class="space-y-8 text-center">
		<!-- Hero Title -->
		<div class="space-y-4">
			<h1
				class="font-heading text-foreground text-4xl leading-tight font-bold md:text-6xl lg:text-7xl"
			>
				Transforme seu corpo com
				<span
					class="from-primary via-primary to-accent bg-gradient-to-r bg-clip-text text-transparent"
				>
					treinos diários
				</span>
				de alta qualidade.
			</h1>

			<!-- Subtitle -->
			<p class="text-muted-foreground mx-auto max-w-2xl text-lg leading-relaxed md:text-xl">
				Acesse treinos novos todos os dias, criados por professora especialista. Exercite-se em
				qualquer lugar com um preço amigável e viva uma vida mais saudável e ativa.
			</p>
		</div>

		<!-- CTA Buttons -->
		<div class="flex flex-col items-center justify-center gap-4 sm:flex-row">
			<Button
				size="lg"
				class="min-w-[200px]"
				href="/access"
				preload
				onclick={() => (globalState.mode = 'signup')}
			>
				<Icon icon="lucide:play-circle" />
				Assinar agora
			</Button>

			<Button
				variant="outline"
				size="lg"
				class="min-w-[200px]"
				href="/access"
				preload
				onclick={() => (globalState.mode = 'login')}
			>
				<Icon icon="lucide:video" />
				Ver treinos
			</Button>
		</div>

		<!-- Trust Indicators -->
		<div class="pt-12">
			<div
				class="text-muted-foreground flex flex-col items-center justify-center gap-8 text-sm sm:flex-row"
			>
				<!-- Profile Images -->
				<div class="flex items-center gap-2">
					<div class="flex -space-x-2">
						<div
							class="from-primary/20 to-accent/20 border-background flex h-8 w-8 items-center justify-center rounded-full border-2 bg-gradient-to-br"
						>
							<Icon icon="lucide:user" class="text-primary h-4 w-4" />
						</div>
						<div
							class="from-accent/20 to-primary/20 border-background flex h-8 w-8 items-center justify-center rounded-full border-2 bg-gradient-to-br"
						>
							<Icon icon="lucide:heart" class="text-accent h-4 w-4" />
						</div>
						<div
							class="from-primary/20 to-accent/20 border-background flex h-8 w-8 items-center justify-center rounded-full border-2 bg-gradient-to-br"
						>
							<Icon icon="lucide:star" class="text-primary h-4 w-4" />
						</div>
					</div>
					<span class="font-medium">+500 alunas ativas</span>
				</div>

				<!-- Divider -->
				<div class="bg-border hidden h-4 w-px sm:block"></div>

				<!-- Features -->
				<div class="flex items-center gap-6">
					<div class="flex items-center gap-2">
						<Icon icon="lucide:play-circle" class="text-primary h-4 w-4" />
						<span>Treinos diários</span>
					</div>

					<div class="flex items-center gap-2">
						<Icon icon="lucide:home" class="text-primary h-4 w-4" />
						<span>Para qualquer ambiente</span>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Professional Photos Collection -->

	{#if firstBubble}
		<!-- Main Photo - Bottom Right -->
		<div
			class="absolute -right-[250px] -bottom-1 z-10 hidden xl:block"
			in:scale={{ delay: 1500, duration: 500 }}
		>
			<div class="relative">
				<!-- Glow effect -->
				<div
					class="from-primary/20 to-accent/20 absolute inset-0 rounded-full bg-gradient-to-br blur-xl"
				></div>

				<!-- Photo container -->
				<div class="relative">
					<img
						src="https://ik.imagekit.io/og7loqgh2/beauty-girl4.png?updatedAt=1749931633663"
						alt="Professora especialista em treinos - Plataforma de exercícios"
						class="size-[420px] rounded-full object-cover object-center shadow-2xl ring-4 ring-white/20 ring-offset-4 ring-offset-transparent"
					/>

					<!-- Decorative elements -->
					<div
						class="from-primary/30 to-accent/30 absolute -top-2 -right-2 h-8 w-8 animate-pulse rounded-full bg-gradient-to-br"
					></div>
					<div
						class="from-accent/30 to-primary/30 absolute -bottom-2 -left-2 h-6 w-6 animate-pulse rounded-full bg-gradient-to-br"
						style="animation-delay: 0.5s;"
					></div>
				</div>
			</div>
		</div>
	{/if}

	{#if secondBubble}
		<!-- Tertiary Photo - Left Side -->
		<div
			class="absolute top-1/2 -left-[100px] z-10 mt-20 hidden -translate-y-1/2 xl:block"
			in:scale={{ delay: 1000, duration: 500 }}
		>
			<div class="relative">
				<!-- Glow effect -->
				<div
					class="from-primary/10 to-accent/10 absolute inset-0 rounded-full bg-gradient-to-br blur-lg"
				></div>

				<!-- Photo container -->
				<div class="relative">
					<img
						src="https://ik.imagekit.io/og7loqgh2/beauty-girl3.png?updatedAt=1749931543158"
						alt="Treinos adaptáveis para qualquer ambiente"
						class="size-48 rounded-full object-cover object-bottom shadow-lg ring-2 ring-white/10 ring-offset-2 ring-offset-transparent"
					/>

					<!-- Tiny decorative element -->
					<div
						class="from-primary/50 to-accent/50 absolute -right-1 -bottom-1 h-3 w-3 animate-pulse rounded-full bg-gradient-to-br"
						style="animation-delay: 1.5s;"
					></div>
				</div>
			</div>
		</div>
	{/if}

	<!-- Background Elements -->
	<div class="absolute inset-0 -z-10 overflow-hidden">
		<!-- Gradient Orbs -->
		<div
			class="from-primary/10 absolute top-20 left-20 h-72 w-72 animate-pulse rounded-full bg-gradient-to-br to-transparent blur-3xl"
		></div>
		<div
			class="from-accent/10 absolute right-20 bottom-20 h-96 w-96 animate-pulse rounded-full bg-gradient-to-bl to-transparent blur-3xl"
			style="animation-delay: 1s;"
		></div>

		<!-- Grid Pattern -->
		<div class="absolute inset-0 opacity-30">
			<div
				class="via-primary/5 h-full w-full bg-gradient-to-br from-transparent to-transparent"
			></div>
		</div>
	</div>
</section>
