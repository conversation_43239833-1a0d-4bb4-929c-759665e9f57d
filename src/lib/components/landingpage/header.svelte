<script lang="ts">
	import { globalState } from '$lib/states/global.state.svelte';
	import Button from '../ui/button/button.svelte';
	import Icon from '../ui/icon.svelte';
	import AnimatedLogoIcon from './animated-logo-icon.svelte';

	let mobileMenuOpen = $state(false);
	let headerRef: HTMLElement | null = $state(null);

	function toggleMobileMenu() {
		mobileMenuOpen = !mobileMenuOpen;
	}

	function closeMobileMenu() {
		mobileMenuOpen = false;
	}

	function handleClickOutside(event: any) {
		if (headerRef && !headerRef.contains(event.target)) {
			closeMobileMenu();
		}
	}

	$effect(() => {
		if (mobileMenuOpen) {
			document.addEventListener('click', handleClickOutside);
		} else {
			document.removeEventListener('click', handleClickOutside);
		}

		return () => {
			document.removeEventListener('click', handleClickOutside);
		};
	});
</script>

<header
	bind:this={headerRef}
	class="relative mx-auto flex max-w-[1200px] items-center justify-center py-8"
>
	<a class="min-w-xl:left-0 absolute left-3 flex items-center gap-3" href="/">
		<div
			class="from-primary/10 to-primary/20 flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br p-2"
		>
			<AnimatedLogoIcon />
		</div>

		<div class="flex flex-col">
			<h1 class="font-heading text-foreground text-xl leading-none font-bold">Flavia Silva</h1>
			<!-- Subtitle - agora visível em mobile também -->
			<span class="text-muted-foreground text-xs font-medium tracking-wide">
				Plataforma de Treino
			</span>
		</div>
	</a>

	<!-- Desktop Navigation -->
	<nav class="hidden items-center gap-6 md:flex">
		<a
			href="#beneficios"
			class="text-muted-foreground hover:text-foreground flex items-center gap-2 text-sm font-medium transition-colors"
		>
			<Icon icon="lucide:play-square" class="h-4 w-4" />
			Benefícios
		</a>
		<a
			href="#sobre"
			class="text-muted-foreground hover:text-foreground flex items-center gap-2 text-sm font-medium transition-colors"
		>
			<Icon icon="lucide:user" class="h-4 w-4" />
			Sobre
		</a>
		<a
			href="#planos"
			class="text-muted-foreground hover:text-foreground flex items-center gap-2 text-sm font-medium transition-colors"
		>
			<Icon icon="lucide:zap" class="h-4 w-4" />
			Planos
		</a>
	</nav>

	<!-- Desktop CTA -->
	<div class="min-w-xl:right-0 absolute right-3 hidden items-center gap-3 md:flex">
		<Button
			class="flex items-center gap-2 px-6"
			preload
			href="/access"
			onclick={() => (globalState.mode = 'login')}
		>
			<Icon icon="lucide:log-in" />
			Acessar
		</Button>
	</div>

	<!-- Mobile Menu Button -->
	<button
		class="text-muted-foreground hover:text-foreground hover:bg-accent/50 min-w-xl:right-0 absolute right-3 flex items-center justify-center rounded-lg transition-all md:hidden"
		class:rotate-180={mobileMenuOpen}
		onclick={toggleMobileMenu}
	>
		<Icon icon="lucide:chevron-down" class="text-xl" />
	</button>

	<!-- Mobile Dropdown Menu -->
	{#if mobileMenuOpen}
		<div
			class="bg-card border-border animate-in slide-in-from-top-2 absolute top-full right-0 left-0 z-50 mx-6 mt-2 overflow-hidden rounded-xl border shadow-xl duration-200"
		>
			<nav class="space-y-4 p-4">
				<a
					href="#beneficios"
					onclick={closeMobileMenu}
					class="hover:bg-accent/50 text-muted-foreground hover:text-foreground flex items-center gap-3 rounded-lg p-3 transition-colors"
				>
					<Icon icon="lucide:play-square" class="h-5 w-5" />
					<span class="font-medium">Benefícios</span>
				</a>
				<a
					href="#sobre"
					onclick={closeMobileMenu}
					class="hover:bg-accent/50 text-muted-foreground hover:text-foreground flex items-center gap-3 rounded-lg p-3 transition-colors"
				>
					<Icon icon="lucide:user" class="h-5 w-5" />
					<span class="font-medium">Sobre</span>
				</a>
				<a
					href="#planos"
					onclick={closeMobileMenu}
					class="hover:bg-accent/50 text-muted-foreground hover:text-foreground flex items-center gap-3 rounded-lg p-3 transition-colors"
				>
					<Icon icon="lucide:zap" class="h-5 w-5" />
					<span class="font-medium">Planos</span>
				</a>

				<!-- Divisor -->
				<div class="border-border my-4 border-t"></div>

				<!-- Botão Acessar Mobile -->
				<div class="p-2">
					<Button
						class="flex w-full items-center justify-center gap-2"
						preload
						href="/access"
						onclick={() => (globalState.mode = 'login')}
					>
						<Icon icon="lucide:log-in" class="h-4 w-4" />
						Acessar
					</Button>
				</div>
			</nav>
		</div>
	{/if}
</header>
