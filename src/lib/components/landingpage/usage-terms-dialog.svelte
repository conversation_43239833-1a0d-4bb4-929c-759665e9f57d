<script lang="ts">
	import * as Dialog from '$lib/components/ui/dialog';
	import * as ScrollArea from '$lib/components/ui/scroll-area';

	let { open = $bindable(false) } = $props();

	function handleClose() {
		open = false;
	}
</script>

<Dialog.Root bind:open>
	<Dialog.Content class="max-w-2xl p-0">
		<div class="p-6 pb-0">
			<Dialog.Header>
				<Dialog.Title class="text-xl font-semibold">Termos de Uso</Dialog.Title>
				<Dialog.Description class="text-muted-foreground">
					Condições para uso da nossa plataforma de treinos
				</Dialog.Description>
			</Dialog.Header>
		</div>

		<ScrollArea.ScrollArea class="max-h-[60vh] px-6">
			<div class="space-y-6 py-4 pr-4">
				<!-- Seção 1 -->
				<div class="space-y-3">
					<h3 class="text-foreground font-medium">Acesso à Plataforma</h3>
					<p class="text-muted-foreground text-sm leading-relaxed">
						Nossa plataforma oferece treinos personalizados e acompanhamento fitness. Para acessar
						todos os recursos, é necessário criar uma conta e manter seus dados atualizados. Você é
						responsável por manter a confidencialidade das suas credenciais de acesso.
					</p>
				</div>

				<hr class="border-border" />

				<!-- Seção 2 -->
				<div class="space-y-3">
					<h3 class="text-foreground font-medium">Uso Adequado</h3>
					<p class="text-muted-foreground text-sm leading-relaxed">
						Os treinos e conteúdos são destinados ao seu uso pessoal. Não é permitido compartilhar,
						revender ou distribuir o material. Recomendamos consultar um profissional de saúde antes
						de iniciar qualquer programa de exercícios, especialmente se você tem condições médicas
						específicas.
					</p>
				</div>

				<hr class="border-border" />

				<!-- Seção 3 -->
				<div class="space-y-3">
					<h3 class="text-foreground font-medium">Responsabilidades</h3>
					<p class="text-muted-foreground text-sm leading-relaxed">
						Você é responsável por praticar os exercícios com segurança e dentro dos seus limites.
						Nossa plataforma fornece orientações gerais, mas cada pessoa deve adaptar os treinos à
						sua condição física e limitações individuais.
					</p>
				</div>

				<hr class="border-border" />

				<!-- Seção 4 -->
				<div class="space-y-3">
					<h3 class="text-foreground font-medium">Assinatura e Pagamentos</h3>
					<p class="text-muted-foreground text-sm leading-relaxed">
						Os planos de assinatura são cobrados conforme o período contratado. O cancelamento pode
						ser feito a qualquer momento através da sua conta, mas o acesso permanece ativo até o
						final do período pago. Não oferecemos reembolsos proporcionais.
					</p>
				</div>

				<hr class="border-border" />

				<!-- Seção 5 -->
				<div class="space-y-3">
					<h3 class="text-foreground font-medium">Modificações</h3>
					<p class="text-muted-foreground text-sm leading-relaxed">
						Podemos atualizar estes termos ocasionalmente para refletir melhorias na plataforma ou
						mudanças legais. Sempre notificaremos sobre alterações importantes e você pode revisar a
						versão atual a qualquer momento.
					</p>
				</div>

				<hr class="border-border" />

				<!-- Seção 6 -->
				<div class="space-y-3">
					<h3 class="text-foreground font-medium">Suporte</h3>
					<p class="text-muted-foreground text-sm leading-relaxed">
						Em caso de dúvidas sobre os termos ou problemas com a plataforma, nossa equipe de
						suporte está sempre disponível através dos canais de contato disponíveis no site.
					</p>
				</div>
			</div>
		</ScrollArea.ScrollArea>

		<div class="p-6 pt-4">
			<Dialog.Footer class="flex justify-end gap-3 p-0">
				<button
					onclick={handleClose}
					class="from-primary via-primary to-primary/80 text-primary-foreground shadow-primary/25 hover:shadow-primary/30 hover:from-primary/95 hover:to-primary/75 inline-flex min-w-24 shrink-0 cursor-pointer items-center justify-center gap-2 rounded-xl border-0 bg-gradient-to-br px-4 py-2 text-sm font-medium whitespace-nowrap shadow-lg transition-all duration-200 outline-none hover:shadow-xl disabled:pointer-events-none disabled:opacity-50"
				>
					Entendi
				</button>
			</Dialog.Footer>
		</div>
	</Dialog.Content>
</Dialog.Root>
