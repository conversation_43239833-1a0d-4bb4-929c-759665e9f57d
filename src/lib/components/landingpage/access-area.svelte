<script lang="ts">
	import Button from '../ui/button/button.svelte';
	import Icon from '../ui/icon.svelte';
	import { Alert, AlertDescription } from '../ui/alert';
	import { Input } from '../ui/input';
	import { Label } from '../ui/label';
	import { scale, fly, fade } from 'svelte/transition';
	import { globalState } from '$lib/states/global.state.svelte';
	import { AuthState } from '$lib/states/auth.state.svelte';

	const authState = new AuthState();
	let successMessage = $state('');

	// Função para alternar entre login e signup
	function toggleMode() {
		globalState.mode = globalState.mode === 'login' ? 'signup' : 'login';
		clearMessages();
		authState.clearForms();
	}

	// Limpar mensagens (local success message)
	function clearMessages() {
		successMessage = '';
		authState.clearMessages();
	}

	// Enhanced auth handlers that manage local success messages
	async function handleLogin() {
		const result = await authState.handleLogin();
		if (result.success) {
			successMessage = 'Login realizado com sucesso!';
		}
	}

	async function handleSignup() {
		const result = await authState.handleSignup();
		if (result.success) {
			successMessage = 'Conta criada com sucesso!';
		}
	}

	async function handleGoogleAuth() {
		const result = await authState.handleGoogleAuth();
		if (result.success) {
			successMessage = 'Autenticação com Google realizada!';
		}
	}

	async function handleForgotPassword() {
		const result = await authState.handleForgotPassword();
		if (result.success) {
			successMessage = result.message || 'Email de recuperação enviado!';
		}
	}
</script>

<section class="relative mx-auto max-w-[500px] px-6 py-36" in:fade>
	<div class="mb-8 text-center">
		<h1 class="font-heading text-foreground mb-2 text-3xl font-bold md:text-4xl">
			{globalState.mode === 'login'
				? 'Entrar na conta'
				: globalState.mode === 'signup'
					? 'Criar conta'
					: 'Recuperar senha'}
		</h1>
		<p class="text-muted-foreground text-base">
			{globalState.mode === 'login'
				? 'Acesse sua conta e continue seus treinos'
				: globalState.mode === 'signup'
					? 'Crie sua conta e comece sua jornada fitness'
					: 'Digite seu email para receber as instruções de recuperação'}
		</p>
	</div>

	<!-- Formulário Principal -->
	<div class="bg-card/50 border-border space-y-6 rounded-2xl border p-8 shadow-lg">
		<!-- Botão Google (Destacado) -->
		<Button
			variant="outline"
			size="lg"
			class="w-full border-gray-200 bg-white font-medium text-gray-700 shadow-md hover:bg-gray-50 hover:shadow-lg"
			onclick={handleGoogleAuth}
			disabled={authState.isLoading}
		>
			<div class="flex items-center gap-3">
				<svg class="h-5 w-5" viewBox="0 0 24 24">
					<path
						fill="#4285F4"
						d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
					/>
					<path
						fill="#34A853"
						d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
					/>
					<path
						fill="#FBBC05"
						d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
					/>
					<path
						fill="#EA4335"
						d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
					/>
				</svg>
				<span>Continuar com Google</span>
			</div>
		</Button>

		<!-- Divisor -->
		<div class="relative">
			<div class="absolute inset-0 flex items-center">
				<div class="border-border w-full border-t"></div>
			</div>
			<div class="relative flex justify-center text-xs uppercase">
				<span class="bg-card text-muted-foreground px-2">ou</span>
			</div>
		</div>

		<!-- Formulário de Login -->
		{#if globalState.mode === 'login'}
			<form class="space-y-4" onsubmit={handleLogin} in:scale={{ duration: 200 }}>
				<div class="space-y-2">
					<Label for="login-email">Email</Label>
					<Input
						aria-invalid={authState.fieldErrors.userEmail ? 'true' : 'false'}
						aria-describedby={authState.fieldErrors.userEmail ? 'login-email-error' : undefined}
						id="login-email"
						type="email"
						placeholder="<EMAIL>"
						bind:value={authState.userEmail}
						disabled={authState.isLoading}
						class={authState.fieldErrors.userEmail ? 'border-red-500' : ''}
						onblur={() => authState.validateFieldOnBlur('userEmail', authState.userEmail)}
					/>
					{#if authState.fieldErrors.userEmail}
						<p class="text-sm text-red-500">{authState.fieldErrors.userEmail}</p>
					{/if}
				</div>

				<div class="space-y-2">
					<Label for="login-password">Senha</Label>
					<Input
						aria-invalid={authState.fieldErrors.userPassword ? 'true' : 'false'}
						aria-describedby={authState.fieldErrors.userPassword
							? 'login-password-error'
							: undefined}
						id="login-password"
						type="password"
						placeholder="••••••••"
						bind:value={authState.userPassword}
						disabled={authState.isLoading}
						class={authState.fieldErrors.userPassword ? 'border-red-500' : ''}
						onblur={() => authState.validateFieldOnBlur('userPassword', authState.userPassword)}
					/>
					{#if authState.fieldErrors.userPassword}
						<p class="text-sm text-red-500">{authState.fieldErrors.userPassword}</p>
					{/if}
				</div>

				<!-- Link Esqueci minha senha -->
				<div class="text-right">
					<button
						type="button"
						onclick={() => {
							globalState.mode = 'forgot-password';
							clearMessages();
						}}
						disabled={authState.isLoading}
						class="text-muted-foreground hover:text-primary text-xs transition-colors disabled:opacity-50"
					>
						Esqueci minha senha
					</button>
				</div>

				<Button type="submit" size="lg" class="w-full" disabled={authState.isLoading}>
					{#if authState.isLoading}
						<Icon icon="lucide:loader-2" class="animate-spin" />
						Entrando...
					{:else}
						<Icon icon="lucide:log-in" />
						Entrar
					{/if}
				</Button>
			</form>
		{/if}

		<!-- Formulário de Signup -->
		{#if globalState.mode === 'signup'}
			<form class="space-y-4" onsubmit={handleSignup} in:scale={{ duration: 200 }}>
				<div class="space-y-2">
					<Label for="signup-name">Nome completo</Label>
					<Input
						id="signup-name"
						type="text"
						placeholder="Seu nome"
						bind:value={authState.userName}
						disabled={authState.isLoading}
						class={authState.fieldErrors.userName ? 'border-red-500' : ''}
						onblur={() => authState.validateFieldOnBlur('userName', authState.userName)}
					/>
					{#if authState.fieldErrors.userName}
						<p class="text-sm text-red-500">{authState.fieldErrors.userName}</p>
					{/if}
				</div>

				<div class="space-y-2">
					<Label for="signup-email">Email</Label>
					<Input
						id="signup-email"
						type="email"
						placeholder="<EMAIL>"
						bind:value={authState.userEmail}
						disabled={authState.isLoading}
						class={authState.fieldErrors.userEmail ? 'border-red-500' : ''}
						onblur={() => authState.validateFieldOnBlur('userEmail', authState.userEmail)}
					/>
					{#if authState.fieldErrors.userEmail}
						<p class="text-sm text-red-500">{authState.fieldErrors.userEmail}</p>
					{/if}
				</div>

				<div class="space-y-2">
					<Label for="signup-password">Senha</Label>
					<Input
						id="signup-password"
						type="password"
						placeholder="••••••••"
						bind:value={authState.userPassword}
						disabled={authState.isLoading}
						class={authState.fieldErrors.userPassword ? 'border-red-500' : ''}
						onblur={() => authState.validateFieldOnBlur('userPassword', authState.userPassword)}
					/>
					{#if authState.fieldErrors.userPassword}
						<p class="text-sm text-red-500">{authState.fieldErrors.userPassword}</p>
					{/if}
				</div>

				<div class="space-y-2">
					<Label for="signup-confirm-password">Confirmar senha</Label>
					<Input
						id="signup-confirm-password"
						type="password"
						placeholder="••••••••"
						bind:value={authState.userConfirmPassword}
						disabled={authState.isLoading}
						class={authState.fieldErrors.userConfirmPassword ? 'border-red-500' : ''}
						onblur={() =>
							authState.validateFieldOnBlur('userConfirmPassword', authState.userConfirmPassword)}
					/>
					{#if authState.fieldErrors.userConfirmPassword}
						<p class="text-sm text-red-500">{authState.fieldErrors.userConfirmPassword}</p>
					{/if}
				</div>

				<Button type="submit" size="lg" class="w-full" disabled={authState.isLoading}>
					{#if authState.isLoading}
						<Icon icon="lucide:loader-2" class="animate-spin" />
						Criando conta...
					{:else}
						<Icon icon="lucide:user-plus" />
						Criar conta
					{/if}
				</Button>
			</form>
		{/if}

		<!-- Formulário de Forgot Password -->
		{#if globalState.mode === 'forgot-password'}
			<form class="space-y-4" onsubmit={handleForgotPassword} in:scale={{ duration: 200 }}>
				<div class="space-y-2">
					<Label for="forgot-email">Email</Label>
					<Input
						id="forgot-email"
						type="email"
						placeholder="<EMAIL>"
						bind:value={authState.forgotEmail}
						disabled={authState.isLoading}
						class={authState.fieldErrors.forgotEmail ? 'border-red-500' : ''}
						onblur={() => authState.validateFieldOnBlur('forgotEmail', authState.forgotEmail)}
					/>
					{#if authState.fieldErrors.forgotEmail}
						<p class="text-sm text-red-500">{authState.fieldErrors.forgotEmail}</p>
					{/if}
				</div>

				<Button type="submit" size="lg" class="w-full" disabled={authState.isLoading}>
					{#if authState.isLoading}
						<Icon icon="lucide:loader-2" class="animate-spin" />
						Enviando...
					{:else}
						<Icon icon="lucide:mail" />
						Enviar email de recuperação
					{/if}
				</Button>
			</form>
		{/if}

		<!-- Toggle entre Login/Signup/Forgot Password -->
		<div class="pt-4 text-center">
			{#if globalState.mode === 'forgot-password'}
				<button
					onclick={() => {
						globalState.mode = 'login';
						authState.clearMessages();
						authState.clearForms();
					}}
					disabled={authState.isLoading}
					class="text-muted-foreground hover:text-primary text-sm transition-colors disabled:opacity-50"
				>
					Voltar para o login
				</button>
			{:else}
				<button
					onclick={toggleMode}
					disabled={authState.isLoading}
					class="text-muted-foreground hover:text-primary text-sm transition-colors disabled:opacity-50"
				>
					{globalState.mode === 'login'
						? 'Não tem uma conta? Criar conta'
						: 'Já tem uma conta? Fazer login'}
				</button>
			{/if}
		</div>
	</div>

	<!-- Feedback Messages -->
	{#if authState.errorMessage}
		<div class="mt-6" in:fly={{ y: 10, duration: 200 }}>
			<Alert variant="destructive">
				<Icon icon="lucide:alert-circle" />
				<AlertDescription>{authState.errorMessage}</AlertDescription>
			</Alert>
		</div>
	{/if}

	{#if successMessage}
		<div class="mt-6" in:fly={{ y: 10, duration: 200 }}>
			<Alert>
				<Icon icon="lucide:check-circle" />
				<AlertDescription class="text-primary">{successMessage}</AlertDescription>
			</Alert>
		</div>
	{/if}

	<!-- Background Elements (sutil) -->
	<div class="absolute inset-0 -z-10 overflow-hidden">
		<div
			class="from-primary/5 absolute top-20 left-20 h-72 w-72 animate-pulse rounded-full bg-gradient-to-br to-transparent blur-3xl"
		></div>
		<div
			class="from-accent/5 absolute right-20 bottom-20 h-96 w-96 animate-pulse rounded-full bg-gradient-to-bl to-transparent blur-3xl"
			style="animation-delay: 1s;"
		></div>
	</div>
</section>
