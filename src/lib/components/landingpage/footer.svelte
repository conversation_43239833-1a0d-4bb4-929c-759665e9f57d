<script lang="ts">
	import Icon from '../ui/icon.svelte';
	import { scale } from 'svelte/transition';
	import AnimatedLogoIcon from './animated-logo-icon.svelte';
	import PrivacyDialog from './privacy-dialog.svelte';
	import UsageTermsDialog from './usage-terms-dialog.svelte';

	const currentYear = new Date().getFullYear();

	let showPrivacyDialog = $state(false);
	let showTermsDialog = $state(false);

	const socialLinks = [
		{ name: 'Instagram', icon: 'lucide:instagram', url: 'https://instagram.com/' },
		{ name: 'WhatsApp', icon: 'lucide:message-circle', url: 'https://wa.me/5500000000000' }
	];

	const footerLinks = [
		{
			title: 'Plataforma',
			links: [
				{ name: 'Sobre', url: '#sobre' },
				{ name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', url: '#beneficios' },
				{ name: 'Planos', url: '#planos' }
			]
		},
		{
			title: 'Suporte',
			links: [
				{ name: 'Política de Privacidade', action: () => (showPrivacyDialog = true) },
				{ name: 'Termos de Uso', action: () => (showTermsDialog = true) }
			]
		}
	];
</script>

<footer class="border-border bg-card/50 border-t backdrop-blur-sm">
	<div class="mx-auto max-w-[1200px] px-6 py-12">
		<!-- Main Footer Content -->
		<div class="grid gap-12 md:grid-cols-2 lg:grid-cols-4">
			<!-- Brand Column -->
			<div class="space-y-6">
				<div class="flex items-center gap-3">
					<div
						class="from-primary/10 to-primary/20 flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br p-2"
					>
						<AnimatedLogoIcon />
					</div>
					<div class="flex flex-col">
						<h2 class="font-heading text-foreground text-xl leading-none font-bold">
							Flavia Silva
						</h2>
						<span class="text-muted-foreground text-xs font-medium tracking-wide">
							Plataforma de Treino
						</span>
					</div>
				</div>

				<p class="text-muted-foreground text-sm leading-relaxed">
					Transforme seu corpo e sua vida com treinos diários personalizados. Acesse de qualquer
					lugar e comece sua jornada de transformação hoje.
				</p>

				<!-- Social Links -->
				<div class="flex gap-4">
					{#each socialLinks as social, i}
						<a
							href={social.url}
							target="_blank"
							rel="noopener noreferrer"
							class="hover:bg-accent/50 hover:text-accent text-muted-foreground flex h-10 w-10 items-center justify-center rounded-full transition-colors"
							aria-label={social.name}
							in:scale={{ delay: i * 100, duration: 200 }}
						>
							<Icon icon={social.icon} class="" />
						</a>
					{/each}
				</div>
			</div>

			<!-- Link Columns -->
			{#each footerLinks as column, i}
				<div class="space-y-6" in:scale={{ delay: 100 + i * 100, duration: 200 }}>
					<h3 class="text-foreground font-medium">{column.title}</h3>
					<ul class="space-y-3">
						{#each column.links as link}
							<li>
								{#if 'action' in link}
									<button
										onclick={link.action}
										class="text-muted-foreground hover:text-primary text-left text-sm transition-colors"
									>
										{link.name}
									</button>
								{:else}
									<a
										href={link.url}
										class="text-muted-foreground hover:text-primary text-sm transition-colors"
									>
										{link.name}
									</a>
								{/if}
							</li>
						{/each}
					</ul>
				</div>
			{/each}

			<!-- Contact Column -->
			<div class="space-y-6" in:scale={{ delay: 300, duration: 200 }}>
				<h3 class="text-foreground font-medium">Contato</h3>
				<ul class="space-y-4">
					<li class="flex items-start gap-3">
						<Icon icon="lucide:mail" class="text-primary mt-0.5 " />
						<a
							href="mailto:<EMAIL>"
							class="text-muted-foreground hover:text-primary text-sm transition-colors"
						>
							<EMAIL>
						</a>
					</li>
					<li class="flex items-start gap-3">
						<Icon icon="lucide:message-circle" class="text-primary mt-0.5 " />
						<a
							href="tel:+5500000000000"
							class="text-muted-foreground hover:text-primary text-sm transition-colors"
						>
							+55 (00) 00000-0000
						</a>
					</li>
				</ul>
			</div>
		</div>

		<!-- Divider -->
		<div class="border-border my-8 border-t"></div>

		<!-- Bottom Footer -->
		<div
			class="flex flex-col items-center justify-between gap-4 text-center md:flex-row md:text-left"
		>
			<div class="flex flex-col items-center gap-2 md:flex-row md:gap-4">
				<p class="text-muted-foreground text-sm">
					&copy; {currentYear} Flavia Silva. Todos os direitos reservados.
				</p>
				<div class="text-muted-foreground/70 flex items-center gap-2 text-xs">
					<Icon icon="lucide:code" class="h-3 w-3" />
					<span>Desenvolvido por</span>
					<a
						href="https://tarcs.com.br"
						target="_blank"
						rel="noopener noreferrer"
						class="hover:text-primary transition-colors"
					>
						tarcs.com.br
					</a>
				</div>
			</div>

			<div class="flex flex-wrap items-center justify-center gap-6 md:justify-end">
				<button
					onclick={() => (showPrivacyDialog = true)}
					class="text-muted-foreground hover:text-primary text-xs transition-colors"
				>
					Política de Privacidade
				</button>
				<button
					onclick={() => (showTermsDialog = true)}
					class="text-muted-foreground hover:text-primary text-xs transition-colors"
				>
					Termos de Uso
				</button>
			</div>
		</div>
	</div>

	<!-- Background Decorative Elements -->
	<div class="absolute inset-0 -z-10 overflow-hidden">
		<!-- Grid Pattern -->
		<div class="absolute inset-0 opacity-10">
			<div
				class="via-primary/5 h-full w-full bg-gradient-to-br [background-image:radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.1)_1px,transparent_0)] from-transparent to-transparent [background-size:20px_20px]"
			></div>
		</div>
	</div>
</footer>

<!-- Dialogs -->
<PrivacyDialog bind:open={showPrivacyDialog} />
<UsageTermsDialog bind:open={showTermsDialog} />
