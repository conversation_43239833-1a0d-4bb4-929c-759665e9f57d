<script lang="ts">
	import * as Dialog from '$lib/components/ui/dialog';
	import * as ScrollArea from '$lib/components/ui/scroll-area';

	let { open = $bindable(false) } = $props();

	function handleAccept() {
		localStorage.setItem('privacy-consent', 'true');
		localStorage.setItem('privacy-consent-date', new Date().toISOString());
		open = false;
	}
</script>

<Dialog.Root bind:open>
	<Dialog.Content class="max-w-2xl p-0">
		<div class="p-6 pb-0">
			<Dialog.Header>
				<Dialog.Title class="text-xl font-semibold">Política de Privacidade</Dialog.Title>
				<Dialog.Description class="text-muted-foreground">
					Como tratamos suas informações na nossa plataforma
				</Dialog.Description>
			</Dialog.Header>
		</div>

		<ScrollArea.ScrollArea class="max-h-[60vh] px-6">
			<div class="space-y-6 py-4 pr-4">
				<!-- Seção 1 -->
				<div class="space-y-3">
					<h3 class="text-foreground font-medium">Informações que Coletamos</h3>
					<p class="text-muted-foreground text-sm leading-relaxed">
						Coletamos apenas as informações necessárias para oferecer a melhor experiência na nossa
						plataforma. Isso inclui dados básicos de perfil, preferências de treino e informações
						sobre como você utiliza nossos serviços para personalizar seu conteúdo.
					</p>
				</div>

				<hr class="border-border" />

				<!-- Seção 2 -->
				<div class="space-y-3">
					<h3 class="text-foreground font-medium">Como Usamos suas Informações</h3>
					<p class="text-muted-foreground text-sm leading-relaxed">
						Utilizamos suas informações para personalizar treinos, acompanhar seu progresso e
						melhorar continuamente nossa plataforma. Também coletamos dados sobre navegação e
						interações para otimizar a experiência do usuário e desenvolver novos recursos.
					</p>
				</div>

				<hr class="border-border" />

				<!-- Seção 3 -->
				<div class="space-y-3">
					<h3 class="text-foreground font-medium">Proteção dos seus Dados</h3>
					<p class="text-muted-foreground text-sm leading-relaxed">
						Seus dados são protegidos com as melhores práticas de segurança. Não compartilhamos
						informações pessoais com terceiros sem seu consentimento, exceto quando necessário para
						o funcionamento dos serviços essenciais da plataforma.
					</p>
				</div>

				<hr class="border-border" />

				<!-- Seção 4 -->
				<div class="space-y-3">
					<h3 class="text-foreground font-medium">Seus Direitos</h3>
					<p class="text-muted-foreground text-sm leading-relaxed">
						Você tem controle total sobre suas informações. Pode acessar, corrigir ou solicitar a
						exclusão dos seus dados a qualquer momento através do seu perfil ou entrando em contato
						conosco diretamente.
					</p>
				</div>

				<hr class="border-border" />

				<!-- Seção 5 -->
				<div class="space-y-3">
					<h3 class="text-foreground font-medium">Atualizações</h3>
					<p class="text-muted-foreground text-sm leading-relaxed">
						Esta política pode ser atualizada periodicamente para refletir melhorias nos nossos
						serviços. Sempre notificaremos sobre mudanças significativas que possam afetar o
						tratamento dos seus dados.
					</p>
				</div>
			</div>
		</ScrollArea.ScrollArea>

		<div class="p-6 pt-4">
			<Dialog.Footer class="flex justify-end gap-3 p-0">
				<button
					onclick={handleAccept}
					class="from-primary via-primary to-primary/80 text-primary-foreground shadow-primary/25 hover:shadow-primary/30 hover:from-primary/95 hover:to-primary/75 inline-flex min-w-24 shrink-0 cursor-pointer items-center justify-center gap-2 rounded-xl border-0 bg-gradient-to-br px-4 py-2 text-sm font-medium whitespace-nowrap shadow-lg transition-all duration-200 outline-none hover:shadow-xl disabled:pointer-events-none disabled:opacity-50"
				>
					Entendi
				</button>
			</Dialog.Footer>
		</div>
	</Dialog.Content>
</Dialog.Root>
