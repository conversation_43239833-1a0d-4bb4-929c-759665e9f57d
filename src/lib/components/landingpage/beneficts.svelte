<script lang="ts">
	import Button from '../ui/button/button.svelte';
	import Icon from '../ui/icon.svelte';
	import { scale, fly } from 'svelte/transition';
	import { onMount } from 'svelte';
	import { globalState } from '$lib/states/global.state.svelte';

	let selectedCard = $state(0);
	let isHovering = $state(false);
	let autoSelectInterval: number | null = $state(null);

	const attributes = [
		{
			id: 0,
			title: 'Força',
			subtitle: 'Construa músculos definidos',
			icon: 'lucide:dumbbell',
			image: 'https://ik.imagekit.io/og7loqgh2/strength.png?updatedAt=1749935970931',
			mainContent: {
				title: 'Desenvolva Força Real',
				description:
					'Treinos focados em construção muscular com exercícios progressivos de alta qualidade. Fortaleça seu corpo de forma segura e eficiente.',
				cta: 'Comece a ficar forte',
				figurative: 'lucide:trending-up'
			}
		},
		{
			id: 1,
			title: 'Resistência',
			subtitle: 'Energia que não acaba',
			icon: 'lucide:heart',
			image: 'https://ik.imagekit.io/og7loqgh2/resistency.png?updatedAt=1749936247951',
			mainContent: {
				title: 'Resistência Ilimitada',
				description:
					'Melhore sua capacidade cardiovascular e resistência física com treinos que aumentam gradualmente sua energia e disposição para o dia a dia.',
				cta: 'Ganhe mais energia',
				figurative: 'lucide:battery'
			}
		},
		{
			id: 2,
			title: 'Velocidade',
			subtitle: 'Movimentos explosivos',
			icon: 'lucide:zap',
			image: 'https://ik.imagekit.io/og7loqgh2/speed.png?updatedAt=1749936453559',
			mainContent: {
				title: 'Agilidade e Velocidade',
				description:
					'Desenvolva reflexos rápidos e movimentos explosivos com treinos de alta intensidade que melhoram sua coordenação e velocidade.',
				cta: 'Seja mais ágil',
				figurative: 'lucide:rocket'
			}
		},
		{
			id: 3,
			title: 'Flexibilidade',
			subtitle: 'Corpo livre e solto',
			icon: 'lucide:rotate-3d',
			image: 'https://ik.imagekit.io/og7loqgh2/flexbility.png?updatedAt=1749936624858',
			mainContent: {
				title: 'Flexibilidade Total',
				description:
					'Alongamentos e movimentos que aumentam sua amplitude de movimento, reduzem tensões e previnem lesões no seu dia a dia.',
				cta: 'Melhore flexibilidade',
				figurative: 'lucide:wind'
			}
		},
		{
			id: 4,
			title: 'Equilíbrio',
			subtitle: 'Estabilidade e controle',
			icon: 'lucide:scale',
			image: 'https://ik.imagekit.io/og7loqgh2/equilibrium.png?updatedAt=1749936731356',
			mainContent: {
				title: 'Equilíbrio Perfeito',
				description:
					'Fortaleça seu core e melhore sua postura com exercícios que desenvolvem estabilidade, coordenação e consciência corporal.',
				cta: 'Encontre seu centro',
				figurative: 'lucide:target'
			}
		},
		{
			id: 5,
			title: 'Foco',
			subtitle: 'Mente e corpo conectados',
			icon: 'lucide:eye',
			image: 'https://ik.imagekit.io/og7loqgh2/focus.png?updatedAt=1749936805448',
			mainContent: {
				title: 'Concentração Mental',
				description:
					'Exercícios que conectam mente e corpo, melhorando sua concentração, reduzindo stress e aumentando sua disciplina diária.',
				cta: 'Desenvolva foco',
				figurative: 'lucide:brain'
			}
		}
	];

	function startAutoSelect() {
		if (autoSelectInterval) clearInterval(autoSelectInterval);

		autoSelectInterval = setInterval(() => {
			if (!isHovering) {
				selectedCard = (selectedCard + 1) % attributes.length;
			}
		}, 20000);
	}

	function pauseAutoSelect() {
		if (autoSelectInterval) {
			clearInterval(autoSelectInterval);
		}
	}

	// Handlers
	function handleCardSelect(index: any) {
		selectedCard = index;
		startAutoSelect();
	}

	function handleMainCardHover(hovering: any) {
		isHovering = hovering;
	}

	onMount(() => {
		startAutoSelect();

		return () => {
			pauseAutoSelect();
		};
	});

	const currentMainContent = $derived(attributes[selectedCard]?.mainContent);
</script>

<section class="relative mx-auto max-w-[1200px] px-6 py-24">
	<div class="mb-16 text-center">
		<h2 class="font-heading text-foreground mb-4 text-3xl font-bold md:text-4xl lg:text-5xl">
			Transforme seu corpo com
			<span
				class="from-primary via-primary to-accent bg-gradient-to-r bg-clip-text text-transparent"
			>
				treinos inteligentes
			</span>
		</h2>
		<p class="text-muted-foreground mx-auto max-w-2xl text-lg">
			Cada treino é projetado para desenvolver diferentes aspectos da sua forma física. Descubra
			como nossa plataforma trabalha cada área do seu desenvolvimento.
		</p>
	</div>

	<!-- Main Content Grid -->
	<div class="grid gap-8 lg:grid-cols-5">
		<div class="lg:col-span-2">
			<div class="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-2">
				{#each attributes as attribute, index}
					<button
						class="group relative overflow-hidden rounded-2xl border transition-all duration-300 hover:scale-105 {selectedCard ===
						index
							? 'border-primary/50 bg-primary/5 ring-primary/20 shadow-lg ring-2'
							: 'border-border bg-card/50 hover:border-primary/30 hover:bg-primary/2'}"
						onclick={() => handleCardSelect(index)}
						in:scale={{ delay: index * 100, duration: 400 }}
					>
						<!-- Card Background Gradient -->
						<div
							class="absolute inset-0 opacity-0 transition-opacity duration-300 group-hover:opacity-100 {selectedCard ===
							index
								? 'opacity-50'
								: ''}"
						>
							<div
								class="from-primary/10 via-accent/5 h-full w-full bg-gradient-to-br to-transparent"
							></div>
						</div>

						<!-- Card Content -->
						<div class="relative p-6">
							<!-- Icon -->
							<div
								class="mb-4 flex h-12 w-12 items-center justify-center rounded-xl transition-colors duration-300 {selectedCard ===
								index
									? 'bg-primary/20 text-primary'
									: 'bg-muted/50 text-muted-foreground group-hover:bg-primary/10 group-hover:text-primary'}"
							>
								<Icon icon={attribute.icon} />
							</div>

							<!-- Text Content -->
							<div class="space-y-1">
								<h3
									class="font-semibold transition-colors duration-300 {selectedCard === index
										? 'text-primary'
										: 'text-foreground'}"
								>
									{attribute.title}
								</h3>
								<p class="text-muted-foreground text-sm leading-relaxed">
									{attribute.subtitle}
								</p>
							</div>

							<!-- Active Indicator -->
							{#if selectedCard === index}
								<div class="absolute top-3 right-3" in:scale={{ duration: 200 }}>
									<div class="bg-primary flex h-2 w-2 animate-pulse rounded-full"></div>
								</div>
							{/if}
						</div>
					</button>
				{/each}
			</div>
		</div>

		<!-- Main Card -->
		<div class="lg:col-span-3">
			<!-- svelte-ignore a11y_no_static_element_interactions -->
			<div
				class="bg-card/50 relative overflow-hidden rounded-3xl border p-8 shadow-xl backdrop-blur-sm md:p-12"
				onmouseenter={() => handleMainCardHover(true)}
				onmouseleave={() => handleMainCardHover(false)}
			>
				<!-- Background Effects -->
				<div class="absolute inset-0 -z-10">
					<!-- Gradient Background -->
					<div
						class="from-primary/5 via-accent/5 h-full w-full bg-gradient-to-br to-transparent"
					></div>

					<!-- Floating Orbs -->
					<div
						class="from-primary/10 absolute -top-20 -right-20 h-40 w-40 animate-pulse rounded-full bg-gradient-to-br to-transparent blur-3xl"
					></div>
					<div
						class="from-accent/10 absolute -bottom-20 -left-20 h-32 w-32 animate-pulse rounded-full bg-gradient-to-tr to-transparent blur-2xl"
						style="animation-delay: 1s;"
					></div>
				</div>

				<!-- Content Container -->
				<div class="relative space-y-8">
					<!-- Image for the selected attribute -->
					{#key selectedCard}
						<div
							class="overflow-hidden rounded-xl shadow-lg"
							in:scale={{ duration: 400, delay: 150 }}
						>
							<img
								src={attributes[selectedCard]?.image}
								alt={currentMainContent?.title}
								class="h-80 w-full object-bottom transition-transform duration-500 hover:scale-105"
							/>
						</div>
					{/key}

					<!-- Top Content -->
					{#key selectedCard}
						<div class="space-y-4" in:fly={{ y: 20, duration: 400, delay: 100 }}>
							<h3 class="font-heading text-foreground text-2xl font-bold md:text-3xl">
								{currentMainContent?.title}
							</h3>
							<p class="text-muted-foreground leading-relaxed md:text-lg">
								{currentMainContent?.description}
							</p>
						</div>
					{/key}

					<!-- Figurative Element & CTA -->
					{#key selectedCard}
						<div
							class="flex flex-col items-center justify-center space-y-6"
							in:scale={{ duration: 400, delay: 200 }}
						>
							<!-- Large Figurative Icon -->
							<div
								class="from-primary/20 to-accent/20 flex h-24 w-24 items-center justify-center rounded-full bg-gradient-to-br shadow-lg"
							>
								<Icon icon={currentMainContent?.figurative} class="text-primary text-3xl" />
							</div>

							<!-- CTA Button -->
							<Button
								size="lg"
								class="min-w-[200px] shadow-lg"
								href="/access"
								preload
								onclick={() => (globalState.mode = 'signup')}
							>
								<Icon icon="lucide:play" />
								{currentMainContent?.cta}
							</Button>
						</div>
					{/key}

					<!-- Progress Indicator -->
					<div class="flex justify-center">
						<div class="flex space-x-2">
							{#each attributes as _, index}
								<div
									class="h-2 rounded-full transition-all duration-300 {selectedCard === index
										? 'bg-primary w-8'
										: 'bg-muted w-2'}"
								></div>
							{/each}
						</div>
					</div>
				</div>

				<!-- Hover Pause Indicator -->
				{#if isHovering}
					<div class="absolute top-4 left-4" in:scale={{ duration: 200 }}>
						<div
							class="bg-accent/20 text-accent flex items-center gap-2 rounded-full px-3 py-1 text-xs font-medium"
						>
							<Icon icon="lucide:pause" class="h-3 w-3" />
							Pausado
						</div>
					</div>
				{/if}
			</div>
		</div>
	</div>

	<!-- Background Decorative Elements -->
	<div class="absolute inset-0 -z-10 overflow-hidden">
		<!-- Grid Pattern -->
		<div class="absolute inset-0 opacity-30">
			<div
				class="via-primary/5 h-full w-full bg-gradient-to-br [background-image:radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.1)_1px,transparent_0)] from-transparent to-transparent [background-size:20px_20px]"
			></div>
		</div>
	</div>
</section>
