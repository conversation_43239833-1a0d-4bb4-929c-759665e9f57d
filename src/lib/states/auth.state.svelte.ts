import { z } from 'zod';
import { registrationSchema, baseRegistrationSchema } from '$lib/zod/schemas';

export class AuthState {
    // Loading state
    isLoading = $state(false);

    // Error handling
    errorMessage = $state('');
    fieldErrors = $state<Record<string, string>>({});

    // Form field states
    userName = $state('');
    userEmail = $state('');
    userPassword = $state('');
    userConfirmPassword = $state('');
    forgotEmail = $state('');

    // Schemas for different forms
    private loginSchema = z.object({
        userEmail: z.string().email('Digite um email válido'),
        userPassword: z.string().min(1, 'Digite sua senha')
    });

    private forgotPasswordSchema = z.object({
        forgotEmail: z.string().email('Digite um email válido')
    });

    // Validation method for blur events
    validateFieldOnBlur(fieldName: string, value: string) {
        try {
            if (fieldName === 'userConfirmPassword') {
                // For confirm password, validate the entire registration schema
                registrationSchema.parse({
                    userName: this.userName,
                    userEmail: this.userEmail,
                    userPassword: this.userPassword,
                    userConfirmPassword: this.userConfirmPassword
                });
            } else if (fieldName === 'forgotEmail') {
                // For forgot email, use forgot password schema
                this.forgotPasswordSchema.shape.forgotEmail.parse(value);
            } else if (['userName', 'userEmail', 'userPassword'].includes(fieldName)) {
                // For registration fields, use base registration schema
                const zodFieldName = fieldName as keyof typeof baseRegistrationSchema.shape;
                baseRegistrationSchema.shape[zodFieldName].parse(value);
            } else if (['userEmail', 'userPassword'].includes(fieldName)) {
                // For login fields, use login schema
                this.loginSchema.shape[fieldName as keyof typeof this.loginSchema.shape].parse(value);
            }

            // If validation passes, clear the specific error
            if (this.fieldErrors[fieldName]) {
                delete this.fieldErrors[fieldName];
                this.fieldErrors = { ...this.fieldErrors };
            }
        } catch {
            // Keep the error if still invalid
        }
    }

    // Clear all form fields
    clearForms() {
        this.userName = '';
        this.userEmail = '';
        this.userPassword = '';
        this.userConfirmPassword = '';
        this.forgotEmail = '';
        this.fieldErrors = {};
    }

    // Clear messages
    clearMessages() {
        this.errorMessage = '';
        this.fieldErrors = {};
    }

    // Handle login
    async handleLogin() {
        try {
            // Clear previous errors
            this.fieldErrors = {};
            this.errorMessage = '';

            // Validate with Zod
            const validData = this.loginSchema.parse({
                userEmail: this.userEmail,
                userPassword: this.userPassword
            });

            this.isLoading = true;

            // TODO: Implement real PocketBase logic
            console.log('Login attempt:', validData);

            // Simulate request delay
            await new Promise((resolve) => setTimeout(resolve, 1000));

            // Redirect to app on success
            setTimeout(() => {
                window.location.href = '/app';
            }, 1500);

            return { success: true };
        } catch (error) {
            if (error instanceof z.ZodError) {
                // Map Zod errors to fieldErrors
                error.errors.forEach((err) => {
                    const field = err.path[0] as string;
                    this.fieldErrors[field] = err.message;
                });
                this.fieldErrors = { ...this.fieldErrors };
                this.errorMessage = 'Corrija os erros abaixo para continuar';
            } else {
                this.errorMessage = 'Erro ao fazer login. Verifique suas credenciais.';
            }
            return { success: false };
        } finally {
            this.isLoading = false;
        }
    }

    // Handle signup
    async handleSignup() {
        try {
            // Clear previous errors
            this.fieldErrors = {};
            this.errorMessage = '';

            // Validate with Zod
            const validData = registrationSchema.parse({
                userName: this.userName,
                userEmail: this.userEmail,
                userPassword: this.userPassword,
                userConfirmPassword: this.userConfirmPassword
            });

            this.isLoading = true;

            // TODO: Implement real PocketBase logic
            console.log('Signup attempt:', {
                name: validData.userName,
                email: validData.userEmail,
                password: validData.userPassword
            });

            // Simulate request delay
            await new Promise((resolve) => setTimeout(resolve, 1000));

            // Redirect to app on success
            setTimeout(() => {
                window.location.href = '/app';
            }, 1500);

            return { success: true };
        } catch (error) {
            if (error instanceof z.ZodError) {
                // Map Zod errors to fieldErrors
                error.errors.forEach((err) => {
                    const field = err.path[0] as string;
                    this.fieldErrors[field] = err.message;
                });
                this.fieldErrors = { ...this.fieldErrors };
                this.errorMessage = 'Corrija os erros abaixo para continuar';
            } else {
                this.errorMessage = 'Erro ao criar conta. Tente novamente.';
            }
            return { success: false };
        } finally {
            this.isLoading = false;
        }
    }

    // Handle Google authentication
    async handleGoogleAuth() {
        this.clearMessages();
        this.isLoading = true;

        try {
            // TODO: Implement real PocketBase Google Auth logic
            console.log('Google auth attempt');

            // Simulate request delay
            await new Promise((resolve) => setTimeout(resolve, 1000));

            // Redirect to app on success
            setTimeout(() => {
                window.location.href = '/app';
            }, 1500);

            return { success: true };
        } catch (error) {
            this.errorMessage = 'Erro na autenticação com Google. Tente novamente.';
            return { success: false };
        } finally {
            this.isLoading = false;
        }
    }

    // Handle forgot password
    async handleForgotPassword() {
        try {
            // Clear previous errors
            this.fieldErrors = {};
            this.errorMessage = '';

            // Validate with Zod
            const validData = this.forgotPasswordSchema.parse({
                forgotEmail: this.forgotEmail
            });

            this.isLoading = true;

            // TODO: Implement real PocketBase logic
            console.log('Forgot password attempt:', validData);

            // Simulate request delay
            await new Promise((resolve) => setTimeout(resolve, 1000));

            return { success: true, message: 'Email de recuperação enviado! Verifique sua caixa de entrada.' };
        } catch (error) {
            if (error instanceof z.ZodError) {
                // Map Zod errors to fieldErrors
                error.errors.forEach((err) => {
                    const field = err.path[0] as string;
                    this.fieldErrors[field] = err.message;
                });
                this.fieldErrors = { ...this.fieldErrors };
                this.errorMessage = 'Corrija os erros abaixo para continuar';
            } else {
                this.errorMessage = 'Erro ao enviar email de recuperação. Tente novamente.';
            }
            return { success: false };
        } finally {
            this.isLoading = false;
        }
    }
}

// Create and export a singleton instance
export const authState = new AuthState();