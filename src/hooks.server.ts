import { sequence } from '@sveltejs/kit/hooks';
import { securityHeaders } from '$lib/middleware/server/headers';
import { apiToolsSecurity } from '$lib/middleware/server/tools';
import { authMiddleware } from '$lib/middleware/server/auth';
import { errorReporting, handleError } from '$lib/middleware/server/reports';

export const handle = sequence(securityHeaders, errorReporting, apiToolsSecurity, authMiddleware);

export { handleError };